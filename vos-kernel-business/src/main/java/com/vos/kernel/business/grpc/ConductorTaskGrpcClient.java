package com.vos.kernel.business.grpc;

import cn.hutool.core.util.BooleanUtil;
import com.linker.omos.client.config.WorkflowTaskProperties;
import com.netflix.conductor.grpc.TaskServiceGrpc;
import com.netflix.conductor.grpc.TaskServicePb;
import com.netflix.conductor.proto.TaskResultPb;
import com.vos.kernel.business.config.WorkflowProperties;
import com.vos.kernel.common.grpc.TaskExecLog;
import com.vos.kernel.common.grpc.WorkflowTaskServiceGrpc;
import com.vos.kernel.common.workflow.WorkflowCallBackContent;
import com.vos.kernel.common.workflow.WorkflowCallBackContentItem;
import io.grpc.CompressorRegistry;
import io.grpc.Deadline;
import io.grpc.ManagedChannel;
import io.grpc.ManagedChannelBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025年05月26日
 * @version: 1.0
 * @description: TODO
 */
@Slf4j
public class ConductorTaskGrpcClient {

    private ManagedChannel channel;
//    private TaskServiceGrpc.TaskServiceBlockingStub stub;


    /**
     * 构造函数
     *
     * @param workflowProperties
     */
    public ConductorTaskGrpcClient(WorkflowProperties workflowProperties) {
        if (!BooleanUtil.isTrue(workflowProperties.getEnableConductorGrpc())) {
            return;
        }
        log.info("ConductorTaskGrpcClient init");
        WorkflowTaskProperties.GrpcProperties grpc = workflowProperties.getGrpc();
        this.channel = ManagedChannelBuilder.forAddress(grpc.getHost(), grpc.getPort())
                .usePlaintext()
                .enableRetry()
                .maxRetryAttempts(3)
                .idleTimeout(360, TimeUnit.SECONDS)
                .keepAliveTime(30, TimeUnit.SECONDS)
                .keepAliveTimeout(5, TimeUnit.SECONDS)
                .keepAliveWithoutCalls(true)
                .compressorRegistry(CompressorRegistry.getDefaultInstance())
                .build();
//        this.stub = TaskServiceGrpc.newBlockingStub(channel).withDeadline(Deadline.after(workflowProperties.getTimeoutMs(), TimeUnit.MILLISECONDS));
    }


    /**
     * 关闭连接
     *
     * @throws InterruptedException
     */
    private void shutdown() throws InterruptedException {
        channel.shutdown().awaitTermination(10, TimeUnit.SECONDS);
    }


//    /**
//     * 更新任务状态
//     *
//     * @param workflowCallBackContent
//     */
//    public void updateTask(WorkflowCallBackContent workflowCallBackContent) {
//        stub.updateTask(
//                TaskServicePb.UpdateTaskRequest.newBuilder()
//                        .setResult(toProto(workflowCallBackContent))
//                        .build());
//    }
//
//    /**
//     * 转换为proto数据结构
//     *
//     * @param workflowCallBackContent
//     * @return
//     */
//    private TaskResultPb.TaskResult toProto(WorkflowCallBackContent workflowCallBackContent) {
//        TaskResultPb.TaskResult.Builder builder = TaskResultPb.TaskResult.newBuilder();
//        builder.setTaskId(workflowCallBackContent.getTransmissionParams().getString("taskId"));
//        builder.setWorkflowInstanceId(workflowCallBackContent.getTransmissionParams().getString("workflowInstanceId"));
//        WorkflowCallBackContentItem taskResult = workflowCallBackContent.getTaskResult();
//
//        builder.setStatus(TaskResultPb.TaskResult.Status.valueOf(taskResult.getStatus().name()));
//        builder.setWorkerId(taskResult.getWorkerId());
//        // 处理输入为空的情况
//        List<TaskExecLog> taskExecLogs = Optional.ofNullable(taskResult.getLogs())
//                .orElse(Collections.emptyList())
//                .stream()
//                .filter(Objects::nonNull)
//                .map(logData -> {
//                    TaskExecLog taskExecLog = new TaskExecLog();
//                    taskExecLog.setLog(logData.getLog());
//                    taskExecLog.setTaskId(taskResult.getTaskId());
//                    taskExecLog.setCreatedTime(logData.getCreatedTime());
//                    return taskExecLog;
//                })
//                .collect(Collectors.toList());
//        builder.(taskExecLogs);
//        builder.setReasonForIncompletion(taskResult.getReasonForIncompletion());
//        builder.setOutputData(taskResult.getOutputData());
//        if (taskResult.getStartTime() != null && taskResult.getStartTime() > 0) {
//            builder.setStartTime(taskResult.getStartTime());
//        }
//        return builder.build();
//    }
//
//
//    /**
//     * 批量更新
//     *
//     * @param workflowCallBackContentList
//     */
//    public void updateTaskList(List<WorkflowCallBackContent> workflowCallBackContentList) {
//        for (WorkflowCallBackContent workflowCallBackContent : workflowCallBackContentList) {
//            updateTask(workflowCallBackContent);
//        }
//    }
//

}
