package com.vos.kernel.business.config;

import cn.hutool.core.collection.ListUtil;
import com.linker.omos.client.config.WorkflowTaskProperties;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年12月09日
 * @version: 1.0
 * @description: TODO
 */
@Data
@Component
@ConfigurationProperties(prefix = "workflow")
@RefreshScope
public class WorkflowProperties {


    /**
     * 请求地址
     */
    private String endpoint;

    /**
     * 拉取超时时间
     */
    private Long timeoutMs = 1000L;

    /**
     * 最大客户端实例数
     */
    private Long maxHttpClientNum = 5L;

    /**
     * 单实例最大连接数
     */
    private int maxRequests = 1024;

    /**
     * 单实例上每个目标主机的最大连接数
     */
    private int maxRequestsPerHost = 32;

    /**
     * 连接池的最大空闲连接数
     */
    private int maxIdleConnections = 30;

    /**
     * 连接保持活动的时间
     */
    private int keepAliveDuration = 360;

    /**
     * 业务元数据长度
     */
    private Integer bizMetaLength = 512;

    /**
     * 访问领域
     */
    private String domain;


    /**
     * 同步模型路由
     */
    private String syncModelRouter = "/provideWorkflow/syncPlatformModel";

    /**
     * 删除编排 工作流
     */
    private String deleteWorkflowRouter = "/provideWorkflow/deleteWorkflow";

    /**
     * 构建请求地址
     */
    private String builderEndpoint = "http://omagent-builder-backend.ide:8080/";

    /**
     * 删除编排 工作流
     */
    private String deleteAgentRouter = "/v1/agent/deleteForOs";

    /**
     * 异步调用工作流
     */
    private String asyncRunWorkflowRouter = "/api/workflow/v1/async-run";

    /**
     * 是否校验图片地址
     */
    private Boolean checkImageUrl;

    /**
     * 校验图片超时时间
     */
    private Integer checkImageTimeOut = 1000;

    /**
     * 最近访问的agent统计数量
     */
    private Integer agentRecentMax = 20;

    /**
     * os token
     */
    private String osToken = "NjU5MzdiYjJiMzMxZTQ2YzcxNzlAcWNTNnBDVUQ=";

    /**
     * 系统租户
     */
    private String systemTenant;

    /**
     * 是否开启conductor grpc
     */
    private Boolean enableConductorGrpc = true;

    /**
     * grpc配置
     */
    private WorkflowTaskProperties.GrpcProperties grpc;

    /**
     * 任务详细配置
     */
    @Data
    public static class GrpcProperties {
        /**
         * 主机名
         */
        private String host;

        /**
         * 端口
         */
        private int port;
    }

    /**
     * 官方自定义节点
     */
    private List<String> ignore = ListUtil.of("process_output", "knowledge_center_complex_search");
}


