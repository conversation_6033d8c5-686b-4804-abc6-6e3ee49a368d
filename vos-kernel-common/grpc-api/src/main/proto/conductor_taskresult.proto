syntax = "proto3";


import "conductor_taskexeclog.proto";
import "conductor_struct.proto";
import "google/protobuf/any.proto";

package workflow;

option java_multiple_files = true;
option java_package = "com.vos.kernel.common.grpc";
option java_outer_classname = "ConductorTaskResult";



message TaskResult {
    enum Status {
        IN_PROGRESS = 0;
        FAILED = 1;
        FAILED_WITH_TERMINAL_ERROR = 2;
        COMPLETED = 3;
    }
    string workflow_instance_id = 1;
    string task_id = 2;
    string reason_for_incompletion = 3;
    int64 callback_after_seconds = 4;
    string worker_id = 5;
    TaskResult.Status status = 6;
//    map<string, conductor.grpc.Value> output_data = 7;
    google.protobuf.Any output_message = 8;
//    repeated TaskExecLog logs = 9;
}
