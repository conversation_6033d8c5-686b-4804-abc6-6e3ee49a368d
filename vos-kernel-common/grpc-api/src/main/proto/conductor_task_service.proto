syntax = "proto3";
package workflow;

import "conductor_taskexeclog.proto";
import "conductor_taskresult.proto";


option java_multiple_files = true;
option java_package = "com.vos.kernel.common.grpc";
option java_outer_classname = "ConductorTaskProto";


service TaskService {


    // POST /
    rpc UpdateTask(UpdateTaskRequest) returns (UpdateTaskResponse);


}



message UpdateTaskRequest {
//    conductor.proto.TaskResult result = 1;
}

message UpdateTaskResponse {
    string task_id = 1;
}
}
